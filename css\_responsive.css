/* Tablet+: Habilitar efeitos visuais */
@media (min-width: 768px) {
    .link-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        background-size: 200% 100%;
        background-position: -200% 0;
        animation: shimmer 3s ease-in-out infinite;
        z-index: 1;
        display: block;
    }

    .link-button::after {
        content: '';
        position: absolute;
        inset: 0;
        padding: 2px;
        background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
        border-radius: inherit;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        display: block;
    }
}

@media (min-width: 768px) {
    .link-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }

    .link-button:hover::before {
        left: 100%;
    }

    .link-button:hover::after {
        opacity: 1;
        animation: border-beam 2s linear infinite;
    }
}

@media (min-width: 768px) {
    .whatsapp:hover {
        border-color: var(--whatsapp-color);
        box-shadow: 0 15px 40px rgba(37, 211, 102, 0.3);
    }

    .instagram:hover {
        border-color: var(--instagram-color);
        box-shadow: 0 15px 40px rgba(228, 64, 95, 0.3);
    }

    .location:hover {
        border-color: var(--location-color);
        box-shadow: 0 15px 40px rgba(66, 133, 244, 0.3);
    }

    .website:hover {
        border-color: var(--website-color);
        box-shadow: 0 15px 40px rgba(108, 92, 231, 0.3);
    }

    .link-button:hover .arrow {
        transform: translateX(5px);
    }
}

/* ===== RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* Mobile Small (320px+) - Base styles already defined above */

/* Mobile Medium (480px+) */
@media (min-width: 480px) {
    .container {
        padding: 20px;
        gap: 24px;
    }

    .link-button {
        padding: 22px 20px;
    }

    .button-content i {
        font-size: 1.75rem;
    }

    .button-text strong {
        font-size: 1.1rem;
    }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
    .container {
        padding: 30px;
        gap: 28px;
        max-width: 600px;
        margin: 0 auto;
    }

    .link-button {
        padding: 24px 28px;
        border-radius: var(--border-radius);
        /* Tablet: Restaurar efeitos visuais */
        box-shadow: var(--shadow);
        backdrop-filter: blur(10px);
    }

    .button-content {
        gap: 24px;
    }

    .button-content i {
        font-size: 2rem;
        width: 50px;
    }

    .button-text strong {
        font-size: 1.2rem;
    }

    .button-text small {
        font-size: 0.9rem;
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 700px;
        gap: 32px;
    }

    .link-button {
        padding: 26px 32px;
        /* Desktop: Hover effects mais pronunciados */
    }

    .button-content {
        gap: 28px;
    }

    .button-content i {
        font-size: 2.2rem;
        width: 55px;
    }

    .button-text strong {
        font-size: 1.3rem;
    }

    .button-text small {
        font-size: 0.95rem;
    }
}

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 800px;
    }

    .link-button {
        padding: 28px 36px;
    }
}

/* ===== OTIMIZAÇÕES TOUCH ESPECÍFICAS ===== */

/* Otimizações Touch */
@media (hover: none) and (pointer: coarse) {
    .link-button:hover,
    .whatsapp:hover,
    .instagram:hover,
    .location:hover,
    .website:hover {
        transform: none !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .link-button:hover .arrow {
        transform: none !important;
    }

    .link-button:focus {
        outline: 2px solid var(--accent-color);
        outline-offset: 2px;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 10px;
    }

    .logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
    }

    .title {
        font-size: 1.8rem;
    }

    .link-button {
        padding: 15px 18px;
    }
}

/* Tablet+: Habilitar efeitos Magic UI */
@media (min-width: 768px) {
    /* Border beam effect para os cards */
    .link-item::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 1px;
        background: linear-gradient(90deg,
            transparent,
            var(--accent-color),
            transparent
        );
        border-radius: inherit;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        display: block;
    }

    /* Spotlight effect nos cards */
    .link-item::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: radial-gradient(
            400px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
            rgba(212, 175, 55, 0.08),
            transparent 40%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        display: block;
    }
}

/* Tablet+: Hover effects completos */
@media (min-width: 768px) {
    .link-item:hover {
        transform: translateY(-2px);
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(212, 175, 55, 0.2);
    }

    .link-item:hover::before {
        opacity: 1;
        animation: border-beam 2s linear infinite;
    }

    .link-item:hover::after {
        opacity: 1;
    }
}

/* ===== BREAKPOINTS PARA CARDS DO MODAL ===== */

/* Tablet (768px+): Restaurar layout horizontal */
@media (min-width: 768px) {
    .link-item {
        padding: 24px;
        border-radius: 16px;
    }

    /* Tablet: Layout horizontal com gaps maiores */
    .link-item-header {
        gap: 20px;
    }

    /* Tablet: Ícone ligeiramente maior */
    .link-preview {
        width: 48px;
        height: 48px;
        font-size: 1.4rem;
        border-radius: 14px;
    }

    .link-preview::before {
        border-radius: 14px;
    }

    /* Tablet: Gap maior entre nome e controles */
    .link-info {
        gap: 10px;
    }

    .link-details h4 {
        font-size: 1.1rem;
        margin-bottom: 4px;
        /* Tablet: Aplicar text truncation novamente */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Tablet: URL permanece oculta */
    .link-details small {
        display: none;
    }

    /* Tablet: Layout horizontal dos controles */
    .link-controls {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px;
        width: auto;
        flex-shrink: 0;
    }

    /* Tablet: Layout já otimizado com flexbox */

    .control-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        min-height: 36px;
        font-size: 0.95rem;
    }
}

/* Desktop (1024px+): Layout otimizado */
@media (min-width: 1024px) {
    .link-item {
        padding: 28px;
    }

    /* Desktop: Layout horizontal com máximo espaçamento */
    .link-item-header {
        gap: 24px;
    }

    /* Desktop: Ícone com tamanho otimizado */
    .link-preview {
        width: 52px;
        height: 52px;
        font-size: 1.5rem;
        border-radius: 16px;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    }

    .link-preview::before {
        border-radius: 16px;
    }

    /* Desktop: Gap máximo entre nome e controles */
    .link-info {
        gap: 12px;
    }

    .link-details h4 {
        font-size: 1.2rem;
    }

    /* Desktop: URL permanece oculta */
    .link-details small {
        display: none;
    }

    .link-controls {
        gap: 16px;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 767px) {
    .mobile-only { display: block; }
    .desktop-only { display: none; }
}

/* Modal Fullscreen para Mobile */
@media (max-width: 767px) {
    .config-modal {
        padding: 0;
        align-items: stretch;
        overflow: hidden;
    }

    .config-modal-content {
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        margin: 0;
        border-radius: 0;
        animation: mobileSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        flex-direction: column;
        overflow: hidden; /* CORREÇÃO MOBILE: Manter apenas overflow hidden - sem scroll no container principal */
        box-sizing: border-box;
    }

    /* Indicador de swipe */
    .swipe-indicator {
        display: flex;
        justify-content: center;
        padding: 12px 0 8px;
        background: var(--secondary-color);
        border-radius: 0;
    }

    .swipe-handle {
        width: 40px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .swipe-handle:active {
        background: var(--accent-color);
        width: 60px;
    }

    /* Header mobile */
    .config-header {
        padding: 16px 20px;
        border-radius: 0;
        flex-shrink: 0;
    }

    .config-header h2 {
        font-size: 1.3rem;
    }

    /* Body com scroll otimizado */
    .config-body {
        flex: 1;
        padding: 0;
        overflow: hidden; /* CORREÇÃO: Remover scroll do body em mobile */
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* Estabilizar altura em mobile */
        min-height: calc(100vh - 200px);
        contain: layout style;
        width: 100%;
        /* Estrutura flexível */
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
    }

    /* Footer fixo no bottom */
    .config-footer {
        padding: 16px 20px;
        border-radius: 0;
        flex-shrink: 0;
        background: var(--primary-color);
        border-top: 1px solid rgba(212, 175, 55, 0.2);
        flex-direction: column;
        gap: 12px;
    }

    .footer-actions {
        width: 100%;
        gap: 12px;
    }

    .footer-actions button {
        flex: 1;
        min-height: 48px;
        font-size: 1rem;
    }
}

/* Mobile: Configurações específicas para navegação por abas */
@media (max-width: 767px) {
    .tab-navigation {
        display: flex;
    }

    .tab-panel {
        padding: 16px;
        /* CORREÇÃO MOBILE: Garantir que apenas tab-panel tenha scroll */
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        /* Altura específica para mobile */
        height: calc(100vh - 200px);
        max-height: calc(100vh - 200px);
    }

    .section-title {
        margin-bottom: 16px;
    }
}

/* ===== OTIMIZAÇÕES TOUCH ===== */

/* Botões otimizados para touch */
@media (max-width: 767px) {
    .btn-expand-form,
    .btn-primary,
    .btn-secondary,
    .btn-add-link,
    .btn-cancel-form {
        min-height: 48px;
        padding: 14px 20px;
        font-size: 1rem;
        border-radius: 12px;
        touch-action: manipulation;
    }

    /* Controles dos links */
    .control-btn {
        min-width: 44px;
        min-height: 44px;
        font-size: 1.1rem;
        border-radius: 8px;
    }

    /* Toggle switches maiores */
    .toggle-switch {
        width: 60px;
        height: 32px;
        border-radius: 32px;
    }

    .toggle-switch::after {
        width: 28px;
        height: 28px;
        top: 2px;
        left: 2px;
    }

    .toggle-switch.active::after {
        transform: translateX(28px);
    }

    /* Inputs otimizados */
    .form-group input:not(.url-input-group input),
    .form-group select:not(.protocol-selector) {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
        border-radius: 12px;
    }

    /* URL Input Group - Mobile otimizado */
    .url-input-group {
        min-height: 48px;
        border-radius: 12px;
    }

    .protocol-selector {
        min-height: 48px;
        padding: 14px 8px;
        font-size: 1rem;
        width: 95px;
        padding-right: 28px;
        background-size: 14px;
        background-position: right 8px center;
    }

    .url-input-group input {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
    }

    /* Cards de links com melhor espaçamento */
    .link-item {
        padding: 20px 16px;
        margin-bottom: 12px;
        border-radius: 16px;
    }

    .link-item-header {
        gap: 22px;
    }

    /* Telas médias: Ícone com tamanho intermediário */
    .link-preview {
        width: 50px;
        height: 50px;
        font-size: 1.45rem;
        border-radius: 15px;
    }

    .link-preview::before {
        border-radius: 15px;
    }

    /* Telas médias: Gap intermediário entre nome e controles */
    .link-info {
        gap: 11px;
    }

    .link-controls {
        gap: 12px;
        flex-wrap: wrap;
    }

    /* Preview de ícones maior */
    .icon-preview {
        width: 48px;
        height: 48px;
        font-size: 1.4rem;
    }
}

/* Mobile: Configurações em coluna */
@media (max-width: 767px) {
    .setting-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 12px;
    }

    .setting-control {
        align-self: center;
    }

    .form-select {
        min-width: 140px;
        min-height: 44px;
        font-size: 1rem;
    }

    .form-input {
        min-height: 44px;
        font-size: 1rem;
        padding: 12px 16px;
    }
}

/* ===== BREAKPOINTS ESPECÍFICOS ===== */

/* Mobile Small (320px - 374px) */
@media (max-width: 374px) {
    .config-button {
        top: 12px;
        right: 12px;
        width: 44px; /* Mínimo de 44px para acessibilidade em dispositivos móveis */
        height: 44px; /* Mínimo de 44px para acessibilidade em dispositivos móveis */
        font-size: 1rem;
    }

    .config-header {
        padding: 12px 16px;
    }

    .config-header h2 {
        font-size: 1.2rem;
    }

    .tab-btn {
        padding: 12px 4px;
        min-height: 56px;
    }

    .tab-btn i {
        font-size: 1.1rem;
    }

    .tab-btn span {
        font-size: 0.7rem;
    }

    .tab-panel {
        padding: 12px;
    }

    .link-item {
        padding: 16px 12px;
    }

    .link-item-header {
        gap: 18px;
    }

    /* Telas pequenas: Manter ícone com destaque */
    .link-preview {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
    }

    /* Telas pequenas: Gap mínimo entre nome e controles */
    .link-info {
        gap: 9px;
    }

    .link-controls {
        align-self: stretch;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 8px;
    }

    .control-btn {
        min-width: 40px;
        min-height: 40px;
        font-size: 1rem;
    }

    .setting-item {
        padding: 16px 12px;
    }

    .footer-actions {
        flex-direction: column;
        gap: 8px;
    }

    .footer-actions button {
        min-height: 44px;
    }
}

/* Mobile Medium (375px - 767px) */
@media (min-width: 375px) and (max-width: 767px) {
    .config-header h2 {
        font-size: 1.4rem;
    }

    .tab-panel {
        padding: 18px;
    }

    .link-item {
        padding: 18px 16px;
    }

    .setting-item {
        padding: 18px 16px;
    }
}

/* Tablet (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .config-modal-content {
        max-width: 700px;
        margin-top: 40px;
    }

    .config-header {
        padding: 24px 28px;
    }

    .config-body {
        padding: 24px 28px;
    }

    .config-footer {
        padding: 24px 28px;
    }

    .tab-navigation {
        display: flex; /* Mostrar navegação por abas em tablet também */
    }

    .tab-panel {
        display: none; /* Usar sistema de abas em tablet */
        padding: 20px;
        flex: 1; /* CORREÇÃO: Usar flex em tablet */
        overflow-y: auto; /* MANTER: Scroll apenas no painel ativo */
    }

    .tab-panel.active {
        display: block; /* Mostrar apenas aba ativa em tablet */
    }

    .section-title {
        display: block; /* Mostrar títulos das seções em tablet */
    }
}

/* Desktop Large (1024px+) */
@media (min-width: 1024px) {
    .config-modal-content {
        max-width: 800px;
    }

    .config-header {
        padding: 28px 32px;
    }

    .config-body {
        padding: 28px 32px;
    }

    .config-footer {
        padding: 28px 32px;
    }
}

/* Responsividade para campos do WhatsApp */
@media (max-width: 767px) {
    .whatsapp-fields {
        padding: 16px;
        margin-top: 16px;
    }

    .whatsapp-fields .form-group {
        margin-bottom: 14px;
    }

    .whatsapp-fields input[type="tel"],
    .whatsapp-fields textarea {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
        border-radius: 12px;
    }

    .whatsapp-fields textarea {
        min-height: 100px;
    }
}

/* Reduz efeitos em dispositivos móveis */
@media (max-width: 767px) {
    .config-modal-content[style*="--reduce-effects"] {
        backdrop-filter: blur(5px) !important;
    }

    .config-modal-content[style*="--reduce-effects"]::before,
    .config-modal-content[style*="--reduce-effects"]::after {
        display: none !important;
    }

    .link-item::before,
    .link-item::after {
        display: none !important;
    }

    .floating-particle {
        display: none !important;
    }
}

/* Melhora áreas de toque */
@media (max-width: 767px) {
    .control-btn {
        position: relative;
    }

    .control-btn::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        background: transparent;
    }
}

/* ===== ANIMAÇÕES MOBILE-FRIENDLY ===== */

/* Animação de entrada otimizada para mobile */
@media (max-width: 767px) {
    .config-modal.active {
        animation: mobileModalFadeIn 0.3s ease-out;
    }

    /* Animação de saída */
    .config-modal.closing {
        animation: mobileModalFadeOut 0.3s ease-out;
    }

    .config-modal-content.closing {
        animation: mobileSlideDown 0.3s ease-out;
    }
}

/* Responsividade para efeitos */
@media (max-width: 768px) {
    .title::before,
    .title::after {
        display: none;
    }

    .floating-particle {
        display: none;
    }

    .link-button:hover {
        transform: translateY(-1px) scale(1.01);
    }
}

/* Responsividade do Modal de Edição */
@media (max-width: 768px) {
    .edit-modal {
        padding: 10px;
        overflow: hidden; /* ✅ CORREÇÃO: Garantir sem scroll no container mobile */
    }

    .edit-modal-content {
        max-width: 100%;
        max-height: calc(100vh - 20px); /* ✅ CORREÇÃO: Altura máxima ajustada para mobile */
    }

    .edit-header {
        padding: 15px 20px;
        flex-shrink: 0; /* ✅ CORREÇÃO: Header não deve encolher */
    }

    .edit-body {
        padding: 20px;
        flex: 1; /* ✅ CORREÇÃO: Corpo ocupa espaço disponível */
        overflow-y: auto; /* ✅ CORREÇÃO: Scroll apenas no corpo */
        overflow-x: hidden; /* ✅ CORREÇÃO: Sem scroll horizontal */
    }

    .edit-actions {
        flex-direction: column;
        flex-shrink: 0; /* ✅ CORREÇÃO: Ações não devem encolher */
    }
}
