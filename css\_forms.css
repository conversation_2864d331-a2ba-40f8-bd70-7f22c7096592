/* Container do Formulário Expansível */
.add-link-form-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    /* Estabilizar layout durante transição */
    will-change: max-height;
    contain: layout style;
    /* Prevenir overflow horizontal */
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

.add-link-form-container.expanded {
    max-height: 600px;
    /* Garantir altura fixa quando expandido para evitar recálculos */
    min-height: 400px;
    /* Estabilizar largura */
    width: 100%;
}

/* Formulários */
.add-link-form {
    background: var(--secondary-color);
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #333;
    margin-top: 0;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-out 0.1s;
}

.add-link-form-container.expanded .add-link-form {
    opacity: 1;
    transform: translateY(0);
}

.form-group {
    margin-bottom: 20px;
    position: relative; /* Adicionado para contexto de empilhamento */
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 120px;
    gap: 15px;
}

.form-group label {
    display: block;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

/* Inputs com Shine Border Effect */
.form-group input:not(.url-input-group input) {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.form-group input:not(.url-input-group input):focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow:
        0 0 0 2px rgba(212, 175, 55, 0.2),
        0 0 20px rgba(212, 175, 55, 0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    transform: translateY(-1px);
}

/* Shine effect para inputs */
.form-group input:not(.url-input-group input):focus::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(212, 175, 55, 0.2),
        transparent
    );
    background-size: 200% 100%;
    background-position: -200% 0;
    animation: shine 1.5s ease-in-out;
}

.form-group small {
    color: var(--text-gray);
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
}

/* URL Input Group - Interface composta para protocolo + URL */
.url-input-group {
    display: flex !important;
    align-items: stretch !important;
    width: 100% !important;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #333;
    background: var(--primary-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: auto;
}

.url-input-group:focus-within {
    border-color: var(--accent-color);
    box-shadow:
        0 0 0 2px rgba(212, 175, 55, 0.2),
        0 0 20px rgba(212, 175, 55, 0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    transform: translateY(-1px);
}

.protocol-selector {
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-right: 1px solid #333;
    color: var(--text-light);
    font-size: 0.9rem;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 85px;
    flex-shrink: 0;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 6px center;
    background-size: 12px;
    padding-right: 24px;
}

.protocol-selector:focus {
    outline: none;
    background-color: rgba(212, 175, 55, 0.1);
}

.protocol-selector option {
    background: var(--primary-color);
    color: var(--text-light);
    padding: 8px;
}

.url-input-group input {
    flex: 1 !important;
    border: none !important;
    background: transparent !important;
    padding: 12px 15px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0 !important; /* Força o flex: 1 a funcionar corretamente */
    min-width: 0 !important; /* Permite que o input encolha se necessário */
    max-width: none !important;
}

.url-input-group input:focus {
    outline: none !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    transform: none !important;
}

.form-group select:not(.protocol-selector) {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.form-group select:not(.protocol-selector):focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-group select option {
    background: var(--primary-color);
    color: var(--text-light);
    padding: 10px;
}

/* Seletor de Ícones */
.icon-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative; /* Adicionado para contexto de empilhamento */
}

.icon-selector select {
    flex: 1;
}

.icon-preview {
    width: 45px;
    height: 45px;
    background: var(--secondary-color);
    border: 1px solid #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--text-gray);
    transition: var(--transition);
}

.icon-preview.active {
    color: var(--text-light);
    border-color: var(--accent-color);
    background: var(--primary-color);
}

/* Ações do Formulário */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.form-select {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-select option {
    background: var(--primary-color);
    color: var(--text-light);
}

.form-input {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    transition: all 0.3s ease;
    width: 100%;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-input::placeholder {
    color: var(--text-gray);
    opacity: 0.7;
}

/* ===== VALIDAÇÃO EM TEMPO REAL ===== */

.form-group input.valid {
    border-color: #27ae60 !important;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
}

.form-group input.invalid {
    border-color: #e74c3c !important;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
}

.form-group input.valid::after {
    content: '✓';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #27ae60;
    font-weight: bold;
}

/* ===== CAMPOS ESPECÍFICOS DO WHATSAPP ===== */

.whatsapp-fields {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.05) 0%, rgba(37, 211, 102, 0.02) 100%);
    border: 1px solid rgba(37, 211, 102, 0.2);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideInUp 0.3s ease-out;
}

.whatsapp-fields::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #25d366, #128c7e);
    opacity: 0.8;
}

.whatsapp-fields .form-group {
    margin-bottom: 16px;
}

.whatsapp-fields .form-group:last-child {
    margin-bottom: 0;
}

.whatsapp-fields label {
    color: #25d366;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.whatsapp-fields label::before {
    content: '📱';
    font-size: 0.9rem;
}

.whatsapp-fields input[type="tel"] {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
}

.whatsapp-fields textarea {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-fields textarea:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
}

.field-hint {
    color: var(--text-gray);
    font-size: 0.75rem;
    margin-top: 6px;
    display: block;
    font-style: italic;
    opacity: 0.8;
}

.character-counter {
    color: var(--text-gray);
    font-size: 0.75rem;
    margin-top: 4px;
    display: block;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.edit-link-form {
    background: var(--secondary-color);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #333;
}

/* Ações do Modal de Edição */
.edit-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}
