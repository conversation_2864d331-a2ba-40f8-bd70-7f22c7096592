/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.logo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: var(--shadow);
    animation: pulse 3s infinite;
    overflow: hidden;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Título modernizado com efeito Aurora */
.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--aurora-colors);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: aurora 3s ease-in-out infinite;
    position: relative;
}

/* Efeito sparkles no título */
.title::before {
    content: '✨';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite;
    font-size: 1.5rem;
}

.title::after {
    content: '✨';
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite 1s;
    font-size: 1.5rem;
}

/* Subtítulo com animação de blur-in */
.subtitle {
    font-size: 1rem;
    color: var(--text-gray);
    font-weight: 300;
    animation: text-blur-in 1s ease-out 0.5s both;
}

/* Efeito de typing cursor para o título */
.title.typing::after {
    content: '|';
    animation: blink 1s infinite;
    color: var(--accent-color);
}
