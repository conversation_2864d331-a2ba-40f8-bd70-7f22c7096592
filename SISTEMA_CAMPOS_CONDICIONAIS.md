# 🔧 Sistema de Campos Condicionais - Documentação Técnica

## 📋 Visão Geral

Este documento descreve a implementação do sistema de campos condicionais que permite criar formulários dinâmicos baseados no tipo de link/serviço selecionado. Atualmente implementado para WhatsApp, mas projetado para ser facilmente extensível para outros serviços.

## 📁 Localização dos Arquivos

### Arquivo Principal
- **`config-system.js`** - Contém toda a lógica do sistema de campos condicionais

### Arquivos de Documentação
- **`WHATSAPP_FEATURE.md`** - Documentação específica da funcionalidade WhatsApp
- **`IMPLEMENTACAO_CONDICIONAL_WHATSAPP.md`** - Detalhes da implementação condicional
- **`CORRECAO_DUPLICACAO_WHATSAPP.md`** - Correções de duplicação implementadas

### Arquivos de Teste
- **`test-whatsapp-duplication-fix.html`** - Teste específico para duplicação
- **`test-realtime-detection.html`** - Teste de detecção em tempo real

## 🏗️ Arquitetura do Sistema

### 1. 🎯 Detecção de Tipo de Link
**Localização**: `config-system.js` - Linhas 700-710

```javascript
detectLinkType(iconClass) {
    const linkTypeMap = {
        'fab fa-whatsapp': 'whatsapp',
        'fab fa-telegram': 'telegram',      // ← ADICIONAR NOVOS TIPOS AQUI
        'fas fa-envelope': 'email',         // ← ADICIONAR NOVOS TIPOS AQUI
        // Adicione novos mapeamentos aqui
    };
    return linkTypeMap[iconClass] || 'default';
}
```

### 2. 🔄 Gerenciamento de Mudanças
**Localização**: `config-system.js` - Linhas 683-711

```javascript
handleIconChange(iconClass, formType) {
    // Sistema de debounce para evitar múltiplas chamadas
    // Detecção do tipo de link
    // Chamada para updateFormFields()
}
```

### 3. 📝 Atualização de Campos
**Localização**: `config-system.js` - Linhas 727-787

```javascript
updateFormFields(linkType, formType) {
    // Sistema de lock para evitar execução simultânea
    // Remoção de campos existentes
    // Criação de campos específicos baseado no tipo
    // Animações de transição
}
```

## 🛠️ Como Adicionar Novos Tipos de Link

### Passo 1: Adicionar Mapeamento de Tipo
**Arquivo**: `config-system.js`
**Localização**: Função `detectLinkType()` (linha ~700)

```javascript
detectLinkType(iconClass) {
    const linkTypeMap = {
        'fab fa-whatsapp': 'whatsapp',
        'fab fa-telegram': 'telegram',      // ← NOVO TIPO
        'fab fa-discord': 'discord',        // ← NOVO TIPO
        'fab fa-linkedin': 'linkedin',      // ← NOVO TIPO
        // Adicione seu novo tipo aqui
    };
    return linkTypeMap[iconClass] || 'default';
}
```

### Passo 2: Adicionar Lógica Condicional
**Arquivo**: `config-system.js`
**Localização**: Função `updateFormFields()` (linha ~760)

```javascript
// Dentro do callback da animação
if (linkType === 'whatsapp') {
    this.createWhatsAppFields(urlContainer, formPrefix);
} else if (linkType === 'telegram') {           // ← NOVO TIPO
    this.createTelegramFields(urlContainer, formPrefix);
} else if (linkType === 'discord') {            // ← NOVO TIPO
    this.createDiscordFields(urlContainer, formPrefix);
} else {
    this.createDefaultFields(urlContainer, formPrefix);
    this.clearSpecificFields(formPrefix, linkType);
}
```

### Passo 3: Criar Função de Campos Específicos
**Arquivo**: `config-system.js`
**Localização**: Após `createWhatsAppFields()` (linha ~827)

```javascript
/**
 * Cria campos específicos para Telegram
 */
createTelegramFields(urlContainer, formPrefix) {
    console.log(`📱 Criando campos Telegram com prefixo: ${formPrefix}`);

    // VERIFICAÇÃO CRÍTICA: Se já existe um container, não cria outro
    const existingContainer = document.getElementById(`${formPrefix}telegram-fields`);
    if (existingContainer) {
        console.log(`⚠️ Container Telegram já existe - pulando criação`);
        urlContainer.style.display = 'none';
        return;
    }

    // Esconde o campo de URL padrão
    urlContainer.style.display = 'none';

    // Cria container para campos do Telegram
    const telegramContainer = document.createElement('div');
    telegramContainer.className = 'telegram-fields';
    telegramContainer.id = `${formPrefix}telegram-fields`;

    telegramContainer.innerHTML = `
        <div class="form-group">
            <label for="${formPrefix}telegram-username">Nome de Usuário *</label>
            <input
                type="text"
                id="${formPrefix}telegram-username"
                placeholder="@meuusuario"
                required
            >
            <small class="field-hint">Nome de usuário do Telegram (com ou sem @)</small>
        </div>
        <!-- Adicione mais campos conforme necessário -->
    `;

    // Insere após o container de URL
    urlContainer.parentNode.insertBefore(telegramContainer, urlContainer.nextSibling);

    // Configura validação específica
    this.setupTelegramValidation(formPrefix);
}
```

### Passo 4: Atualizar Função de Remoção
**Arquivo**: `config-system.js`
**Localização**: Função `removeSpecificFields()` (linha ~789)

```javascript
removeSpecificFields(formPrefix) {
    // Remove TODOS os containers específicos
    const specificContainers = [
        `${formPrefix}whatsapp-fields`,
        `${formPrefix}telegram-fields`,     // ← ADICIONAR NOVO TIPO
        `${formPrefix}discord-fields`,      // ← ADICIONAR NOVO TIPO
    ];

    specificContainers.forEach(containerId => {
        const containers = document.querySelectorAll(`[id*="${containerId}"]`);
        containers.forEach(container => {
            container.remove();
            console.log(`✅ Container removido: ${container.id}`);
        });
    });

    // Remove campos individuais
    const specificFields = [
        `${formPrefix}whatsapp-number`,
        `${formPrefix}whatsapp-message`,
        `${formPrefix}telegram-username`,   // ← ADICIONAR NOVOS CAMPOS
        `${formPrefix}discord-server`,      // ← ADICIONAR NOVOS CAMPOS
    ];
    
    // ... resto da lógica de remoção
}
```

### Passo 5: Adicionar Validação Específica
**Arquivo**: `config-system.js`
**Localização**: Após `setupWhatsAppValidation()` (linha ~884)

```javascript
/**
 * Configura validação em tempo real para campos do Telegram
 */
setupTelegramValidation(formPrefix) {
    const usernameInput = document.getElementById(`${formPrefix}telegram-username`);

    if (usernameInput) {
        usernameInput.addEventListener('input', (e) => {
            let value = e.target.value.trim();
            
            // Adiciona @ se não tiver
            if (value && !value.startsWith('@')) {
                value = '@' + value;
                e.target.value = value;
            }

            // Validação visual
            this.validateTelegramUsername(usernameInput);
        });
    }
}
```

### Passo 6: Adicionar Geração de URL
**Arquivo**: `config-system.js`
**Localização**: Após `generateWhatsAppUrl()` (linha ~963)

```javascript
/**
 * Gera URL do Telegram baseada nos campos
 */
generateTelegramUrl(formPrefix) {
    const usernameInput = document.getElementById(`${formPrefix}telegram-username`);

    if (!usernameInput || !usernameInput.value.trim()) {
        return null;
    }

    const username = usernameInput.value.trim().replace('@', '');

    // Valida username
    if (!this.validateTelegramUsername(usernameInput)) {
        return null;
    }

    return `https://t.me/${username}`;
}
```

### Passo 7: Atualizar Validação nos Formulários
**Arquivo**: `config-system.js`
**Localização**: Funções `handleAddLink()` e `handleEditLink()`

```javascript
// Na validação condicional
if (linkType === 'whatsapp' && document.getElementById(`${formPrefix}whatsapp-fields`)) {
    fullUrl = this.generateWhatsAppUrl(formPrefix);
} else if (linkType === 'telegram' && document.getElementById(`${formPrefix}telegram-fields`)) {
    fullUrl = this.generateTelegramUrl(formPrefix);    // ← ADICIONAR NOVO TIPO
} else if (linkType === 'discord' && document.getElementById(`${formPrefix}discord-fields`)) {
    fullUrl = this.generateDiscordUrl(formPrefix);     // ← ADICIONAR NOVO TIPO
} else {
    // URL padrão
    // ... lógica existente
}
```

## 🔧 Funções Principais do Sistema

### Funções de Detecção
- **`detectLinkType(iconClass)`** - Mapeia ícone para tipo de link
- **`handleIconChange(iconClass, formType)`** - Gerencia mudanças de ícone

### Funções de Gerenciamento de Campos
- **`updateFormFields(linkType, formType)`** - Atualiza interface baseada no tipo
- **`removeSpecificFields(formPrefix)`** - Remove campos específicos existentes
- **`deepCleanForm(formPrefix)`** - Limpeza profunda do formulário

### Funções de Criação (Específicas por Tipo)
- **`createWhatsAppFields(urlContainer, formPrefix)`** - Campos do WhatsApp
- **`createDefaultFields(urlContainer, formPrefix)`** - Campos padrão

### Funções de Validação (Específicas por Tipo)
- **`setupWhatsAppValidation(formPrefix)`** - Validação do WhatsApp
- **`validateWhatsAppNumber(input)`** - Validação de número

### Funções de Geração de URL (Específicas por Tipo)
- **`generateWhatsAppUrl(formPrefix)`** - URL do WhatsApp
- **`parseWhatsAppUrl(url)`** - Parse de URL existente

### Funções de Limpeza (Específicas por Tipo)
- **`clearWhatsAppFields(formPrefix)`** - Limpa campos do WhatsApp

## 🎨 Sistema de Animações

### Funções de Transição
- **`animateFieldTransitionOut(container, callback)`** - Animação de saída (200ms)
- **`animateFieldTransitionIn(container)`** - Animação de entrada (300ms)

## 🔒 Sistema de Proteção

### Prevenção de Duplicação
1. **Debounce**: 100ms em `handleIconChange()`
2. **Lock de processamento**: Flags em `updateFormFields()`
3. **Verificação preventiva**: Check de existência antes de criar
4. **Limpeza robusta**: Remoção completa de elementos órfãos

## 📝 Padrões de Nomenclatura

### IDs de Elementos
- **Container**: `{formPrefix}{tipo}-fields` (ex: `whatsapp-fields`, `edit-telegram-fields`)
- **Campos**: `{formPrefix}{tipo}-{campo}` (ex: `whatsapp-number`, `edit-telegram-username`)

### Classes CSS
- **Container**: `{tipo}-fields` (ex: `whatsapp-fields`, `telegram-fields`)
- **Campos**: Usar classes padrão do sistema

### Funções
- **Criação**: `create{Tipo}Fields(urlContainer, formPrefix)`
- **Validação**: `setup{Tipo}Validation(formPrefix)`, `validate{Tipo}{Campo}(input)`
- **Geração**: `generate{Tipo}Url(formPrefix)`
- **Parse**: `parse{Tipo}Url(url)`
- **Limpeza**: `clear{Tipo}Fields(formPrefix)`

## 🚀 Exemplo Completo: Adicionando Discord

```javascript
// 1. Adicionar ao mapeamento
'fab fa-discord': 'discord',

// 2. Adicionar à lógica condicional
} else if (linkType === 'discord') {
    this.createDiscordFields(urlContainer, formPrefix);

// 3. Criar função de campos
createDiscordFields(urlContainer, formPrefix) {
    // Implementação similar ao WhatsApp
}

// 4. Adicionar validação
setupDiscordValidation(formPrefix) {
    // Validação específica do Discord
}

// 5. Adicionar geração de URL
generateDiscordUrl(formPrefix) {
    // Gera URL do Discord
}
```

## ✅ Checklist para Novos Tipos

- [ ] Adicionar mapeamento em `detectLinkType()`
- [ ] Adicionar lógica em `updateFormFields()`
- [ ] Criar função `create{Tipo}Fields()`
- [ ] Atualizar `removeSpecificFields()`
- [ ] Criar função `setup{Tipo}Validation()`
- [ ] Criar função `generate{Tipo}Url()`
- [ ] Criar função `parse{Tipo}Url()` (se necessário)
- [ ] Criar função `clear{Tipo}Fields()`
- [ ] Atualizar validação em `handleAddLink()` e `handleEditLink()`
- [ ] Testar cenários de duplicação
- [ ] Documentar o novo tipo

Este sistema é **modular**, **extensível** e **robusto**, permitindo adicionar facilmente novos tipos de links mantendo a consistência e qualidade do código! 🎉
