/* Variáveis CSS para cores e espaçamentos */
:root {
    --primary-color: #0a0a0a;
    --secondary-color: #2d2d2d;
    --accent-color: #d4af37;
    --text-light: #ffffff;
    --text-gray: #cccccc;
    --whatsapp-color: #25d366;
    --instagram-color: #e4405f;
    --location-color: #4285f4;
    --website-color: #6c5ce7;
    --border-radius: 15px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Magic UI Variables */
    --shimmer-duration: 3s;
    --aurora-colors: linear-gradient(135deg, #FF0080, #7928CA, #0070F3, #38bdf8);
    --grid-size: 50px;
    --grid-color: rgba(255, 255, 255, 0.1);

    /* Mobile-First Responsive Variables */
    --touch-target-min: 44px;
    --mobile-padding: 16px;
    --mobile-gap: 16px;
    --mobile-border-radius: 12px;
    --mobile-font-size-base: 16px;
    --mobile-font-size-small: 14px;
    --mobile-font-size-large: 18px;

    /* Breakpoints (for reference in comments) */
    /* --mobile-xs: 320px */
    /* --mobile-sm: 480px */
    /* --tablet: 768px */
    /* --desktop: 1024px */
    /* --desktop-lg: 1200px */
}
